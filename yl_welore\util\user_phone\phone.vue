<template>
    <view :class="'cu-modal ' + (check_phone ? 'show' : '')">
        <view class="cu-dialog phone-login-dialog">
            <!-- 标题栏 -->
            <view class="cu-bar bg-gradual-blue justify-end title-bar">
                <view class="content title-text">手机号快速登录</view>
                <view class="action close-btn" @tap="hideModal" style="min-width: 60rpx;min-height: 60rpx;">
                    <text class="cuIcon-close text-white"></text>
                </view>
            </view>

            <!-- 内容区域 -->
            <view class="bg-white content-area">
                <!-- Logo区域 -->
                <view class="logo-section">
                    <view class="logo-container">
                        <image :src="$state.copyright.sgraph" mode="widthFix" class="logo-image"></image>
                    </view>
                    <view class="welcome-text">欢迎来到我们的小程序</view>
                </view>

                <!-- 提示文案区域 -->
                <view class="tips-section">
                    <view class="main-tip">登录后即可畅享完整功能</view>
                    <view class="sub-tip">发布精彩内容 · 参与热门讨论 · 结识志同道合的朋友</view>
                </view>

                <!-- 登录按钮区域 -->
                <view class="login-section">
                    <!-- 协议同意复选框 -->
                    <view class="agreement-section" @tap="toggleAgreement">
                        <view class="checkbox-container">
                            <view :class="'custom-checkbox ' + (agreementChecked ? 'checked' : '')">
                                <text v-if="agreementChecked" class="cuIcon-check checkbox-icon"></text>
                            </view>
                        </view>
                        <view class="agreement-text">
                            我已阅读并同意
                            <text class="privacy-link" @tap.stop="openUserAgreement(3)">《用户服务协议》</text>
                            及
                            <text class="privacy-link" @tap.stop="openUserAgreement(4)">《隐私政策》</text>
                        </view>
                    </view>

                    <button :class="'login-btn ' + (agreementChecked ? '' : 'disabled')"
                            open-type="getPhoneNumber"
                            @getphonenumber="getPhoneNumber"
                            :disabled="!agreementChecked">
                        <text class="login-btn-text">立即登录</text>
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
// tabBarComponent/tabBar.js
var app = getApp();
var http = require('../http.js');

export default {
    /**
     * 组件的属性列表
     */
    props: {
        check_phone: {
            type: Boolean,
            default: false
        }
    },
    /**
     * 组件的初始数据
     */
    data() {
        return {
            agreementChecked: false // 协议同意状态
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        hideModal() {
            this.$emit('close_phone_modal');
        },
        /**
         * 打开用户服务协议
         */
        openUserAgreement(type) {
            wx.openPrivacyContract({
                success: () => { }, // 打开成功
                fail: () => { }, // 打开失败
                complete: () => { }
            })
            // uni.navigateTo({
            //     url: '/yl_welore/pages/packageC/all_agreement/index?type='+type
            // });
        },
        /**
         * 切换协议同意状态
         */
        toggleAgreement() {
            this.agreementChecked = !this.agreementChecked;
        },
        /**
         * 获取手机号
         */
        getPhoneNumber(c) {
            // 检查是否已同意协议
            if (!this.agreementChecked) {
                uni.showModal({
                    title: '提示',
                    content: '请先同意用户服务协议和隐私政策',
                    showCancel: false,
                    success: () => { }
                });
                return;
            }

            console.log(c);
            if (c.detail.errMsg == 'getPhoneNumber:ok') {
                var b = app.globalData.api_root + 'Service/get_user_phone_new';
                var that = this;
                var e = app.globalData.getCache('userinfo');
                console.log(e);
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.uid = e.uid;
                params.code = c.detail.code;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        if (res.data.status == 'success') {
                            //var e = app.globalData.getCache('userinfo');
                            //e.user_phone = res.data.phone;
                            //console.log(e);
                            app.globalData.setCache('userinfo', res.data.userInfo);
                            uni.showToast({
                                title: res.data.msg,
                                icon: 'none',
                                duration: 2000
                            });
                        } else {
                            uni.showModal({
                                title: '提示',
                                content: res.data.msg,
                                showCancel: false,
                                success: () => { }
                            });
                        }
                        that.$emit('close_phone_modal');
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: () => { }
                        });
                    }
                });
            } else {
                uni.showModal({
                    title: '提示',
                    content: c.detail.errMsg,
                    showCancel: false,
                    success: () => { }
                });
            }
        }
    }
}
</script>
<style scoped>
/* 弹窗整体样式 */
.phone-login-dialog {
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
    max-width: 600rpx;
}

/* 标题栏样式 */
.title-bar {
    background-image: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%);
    padding: 30rpx 40rpx;
    position: relative;
}

.title-text {
    color: #ffffff !important;
    font-size: 32rpx;
    font-weight: 600;
    text-align: center;
    width: 100%;
}

.close-btn {
    position: absolute !important;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
}

/* 内容区域 */
.content-area {
    padding: 60rpx 50rpx 50rpx 50rpx;
    background: linear-gradient(180deg, #f8f9ff 0%, #ffffff 100%);
}

/* Logo区域 */
.logo-section {
    text-align: center;
    margin-bottom: 50rpx;
}

.logo-container {
    position: relative;
    display: inline-block;
    margin-bottom: 20rpx;
}

.logo-image {
    width: 140rpx;
    height: 140rpx;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 102, 204, 0.18);
}

.welcome-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}

/* 提示文案区域 */
.tips-section {
    text-align: center;
    margin-bottom: 60rpx;
}

.main-tip {
    font-size: 30rpx;
    color: #333333;
    font-weight: 600;
    margin-bottom: 20rpx;
    line-height: 1.4;
}

.sub-tip {
    font-size: 24rpx;
    color: #666666;
    line-height: 1.5;
    padding: 0 20rpx;
}

/* 登录按钮区域 */
.login-section {
    text-align: center;
}

/* 协议同意区域 */
.agreement-section {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30rpx;
    padding: 20rpx;
    border-radius: 16rpx;
    background: rgba(224, 195, 252, 0.08);
    border: 2rpx solid rgba(224, 195, 252, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.agreement-section:active {
    background: rgba(224, 195, 252, 0.15);
}

.checkbox-container {
    margin-right: 20rpx;
    margin-top: 4rpx;
}

.custom-checkbox {
    width: 36rpx;
    height: 36rpx;
    border: 3rpx solid #e0e0e0;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: #ffffff;
}

.custom-checkbox.checked {
    background-image: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%);
    border-color: #8ec5fc;
}

.checkbox-icon {
    color: #ffffff;
    font-size: 24rpx;
    font-weight: bold;
}

.agreement-text {
    flex: 1;
    font-size: 24rpx;
    color: #666666;
    line-height: 1.5;
    text-align: left;
}

.login-btn {
    width: 100%;
    height: 88rpx;
    background-image: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%);
    border-radius: 44rpx;
    border: none;
    box-shadow: 0 8rpx 24rpx rgba(0, 102, 204, 0.22);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    transition: all 0.3s ease;
}

.login-btn.disabled {
    background-image: linear-gradient(120deg, #f0f0f0 0%, #e0e0e0 100%);
    box-shadow: none;
    opacity: 0.6;
}

.login-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.22);
}

.login-btn-text {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
}

.login-btn.disabled .login-btn-text {
    color: #999999;
}

.privacy-link {
    color: #007aff;
    text-decoration: underline;
}
</style>
